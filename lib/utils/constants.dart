class DarveConstants {
  // final String backendUrl = "http://*************:8080";
  // final String backendUrl = "https://darve.reefscan.info";
  // final String backendUrl = "http://localhost:8080";
}

enum WalletType { deposit, withdraw, transfer }

// Extension to map enum values to strings
extension WalletTypeExtension on WalletType {
  String get value {
    switch (this) {
      case WalletType.deposit:
        return 'Deposit';
      case WalletType.withdraw:
        return 'Withdraw';
      case WalletType.transfer:
        return 'Transfer';
    }
  }
}

enum ChallengeType { all, requested, accepted, rejected, delivered }

extension ChallengeTypeExtension on ChallengeType {
  String get value {
    switch (this) {
      case ChallengeType.all:
        return 'All';
      case ChallengeType.requested:
        return 'Requested';
      case ChallengeType.accepted:
        return 'Accepted';
      case ChallengeType.rejected:
        return 'Rejected';
      case ChallengeType.delivered:
        return 'Delivered';
    }
  }
}

enum RequestCacheType { history, chatList, discussion }

extension RequestCacheTypeExtension on RequestCacheType {
  String get value {
    switch (this) {
      case RequestCacheType.history:
        return 'history_cache';
      case RequestCacheType.chatList:
        return 'chat_list_cache';
      case RequestCacheType.discussion:
        return 'chat_discussion_cache';
    }
  }
}

/// Centralized API endpoint paths for server requests
class ApiPaths {
  // Auth
  static const String login = '/api/login';
  static const String register = '/api/register';
  static const String forgotPassword = '/api/forgot_password';
  static const String resetPassword = '/api/reset_password';
  static const String signWithFacebook = '/api/auth/sign_with_facebook';
  static const String signWithApple = '/api/auth/sign_with_apple';
  static const String signWithGoogle = '/api/auth/sign_with_google';

  // User Management
  static const String setPassword = '/api/users/current/password'; // POST
  static const String changePassword = '/api/users/current/password'; // PATCH
  static const String emailVerificationStart = '/api/users/current/email/verification/start';
  static const String emailVerificationConfirm = '/api/users/current/email/verification/confirm';
  static const String editProfile = '/api/accounts/edit';
  static const String searchUser = '/api/user/search';

  // Profile & Follow System
  static String userProfile(String username) => '/u/$username';
  static String getPosts(String username) => '/api/user/$username/posts';
  static String followUser(String userId) => '/api/follow/$userId'; // POST
  static String unfollowUser(String userId) => '/api/follow/$userId'; // DELETE
  static String getFollowers(String userId) => '/api/user/$userId/followers';
  static String getFollowing(String userId) => '/api/user/$userId/following';
  static String isFollowing(String userId) => '/api/user/follows/$userId';
  static const String getFollowingPosts = '/u/following/posts';

  // Posts
  static const String getAllPosts = '/api/posts';
  static const String createUserPost = '/api/user/post';
  static String createPostInDiscussion(String discussionId) => '/api/discussion/$discussionId/post';
  static String likePost(String postId) => '/api/posts/$postId/like'; // POST
  static String unlikePost(String postId) => '/api/posts/$postId/unlike'; // DELETE
  static String getPostTasks(String postId) => '/api/posts/$postId/tasks';
  static String createPostTask(String postId) => '/api/posts/$postId/tasks';

  // Replies
  static String createReply(String discussionId, String postUri) => '/api/discussion/$discussionId/post/$postUri/reply';
  static String getPostReplies(String discussionId, String postIdent) => '/api/discussion/$discussionId/post/$postIdent/replies';

  // Discussions
  static const String getDiscussions = '/api/discussions';
  static const String createDiscussion = '/api/discussions';
  static String updateDiscussion(String discussionId) => '/api/discussions/$discussionId'; // PATCH
  static String deleteDiscussion(String discussionId) => '/api/discussions/$discussionId'; // DELETE
  static String getDiscussionTasks(String discussionId) => '/api/discussions/$discussionId/tasks';
  static String createDiscussionTask(String discussionId) => '/api/discussions/$discussionId/tasks';
  static String addChatUsers(String discussionId) => '/api/discussions/$discussionId/chat_users'; // POST
  static String removeChatUsers(String discussionId) => '/api/discussions/$discussionId/chat_users'; // DELETE
  static String getDiscussionById(String discussionId) => '/api/discussion/$discussionId';
  static String getDiscussionSse(String discussionId) => '/api/discussion/$discussionId/sse';
  static String getDiscussionSseHtmx(String discussionId) => '/api/discussion/$discussionId/sse/htmx';

  // Discussion Topics
  static String createOrUpdateTopic(String discussionId) => '/api/discussion/$discussionId/topic';
  static String getTopicForm(String discussionId) => '/api/discussion/$discussionId/topic';

  // Tasks
  static const String getReceivedTasks = '/api/tasks/received';
  static const String getGivenTasks = '/api/tasks/given';
  static String acceptTask(String taskId) => '/api/tasks/$taskId/accept';
  static String rejectTask(String taskId) => '/api/tasks/$taskId/reject';
  static String deliverTask(String taskId) => '/api/tasks/$taskId/deliver';
  static String upsertDonor(String taskId) => '/api/tasks/$taskId/donor';

  // Wallet
  static const String getBalance = '/api/user/wallet/balance';
  static const String getUserTxHistory = '/api/user/wallet/history';
  static String createPaymentIntent(String amount) => '/api/user/wallet/endowment/$amount';
  static const String withdrawFunds = '/api/user/wallet/withdraw';

  // Notifications
  static const String getNotifications = '/api/notifications';
  static String readNotification(String id) => '/api/notifications/$id/read';
  static const String readAllNotifications = '/api/notifications/read';
  static const String getCountOfUnreadNotifications = '/api/notifications/count';
  static const String notificationsSse = '/api/notifications/sse';

  // Community
  static const String createOrUpdateCommunity = '/api/community';

  // Access Rules
  static String getAccessRuleForm(String targetRecordId) => '/api/community/$targetRecordId/access-rule';
  static const String createOrUpdateAccessRule = '/api/access-rule';
  static String joinAccessRule(String accessRuleId) => '/api/access-rule/$accessRuleId/join';
  static const String saveAccessGainAction = '/api/join/access-rule';

  // Stripe Integration
  static String accessRulePayment(String arId) => '/api/stripe/access-rule/$arId';
  static const String stripeWebhook = '/api/stripe/webhook';
  static const String stripeEndowmentWebhook = '/api/stripe/endowment/webhook';

  // WebAuthn/Passkey
  static String startPasskeyRegistration(String username) => '/api/passkey/register_start/$username';
  static const String finishPasskeyRegistration = '/api/passkey/register_finish';
  static String startPasskeyLogin(String username) => '/api/passkey/login_start/$username';
  static const String finishPasskeyLogin = '/api/passkey/login_finish';

  // Legacy/Deprecated endpoints (kept for backward compatibility)
  @Deprecated('Use getReceivedTasks instead')
  static const String getAllReceivedTaskRequests = '/api/tasks/received';
  @Deprecated('Use getGivenTasks instead')
  static const String getGivenChallenges = '/api/tasks/given';
  @Deprecated('Use acceptTask instead')
  static String acceptChallenge(String taskId) => '/api/tasks/$taskId/accept';
  @Deprecated('Use deliverTask instead')
  static String deliverTaskRequest(String taskId) => '/api/tasks/$taskId/deliver';
}
