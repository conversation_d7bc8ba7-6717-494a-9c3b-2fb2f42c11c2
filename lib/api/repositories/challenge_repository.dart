import 'package:darve/services/http/http_service.dart';
import 'package:dio/dio.dart';
import 'package:darve/utils/constants.dart';

class ChallengeRepository {
  final HttpService _dioService;

  ChallengeRepository(this._dioService);

  // Tasks API endpoints
  Future<dynamic> getReceivedTasks() async {
    final response = await _dioService.get(ApiPaths.getReceivedTasks);
    return response.data;
  }

  Future<dynamic> getGivenTasks() async {
    final response = await _dioService.get(ApiPaths.getGivenTasks);
    return response.data;
  }

  Future<dynamic> acceptTask(String taskId) async {
    final response = await _dioService.post(ApiPaths.acceptTask(taskId));
    return response.data;
  }

  Future<dynamic> rejectTask(String taskId) async {
    final response = await _dioService.post(ApiPaths.rejectTask(taskId));
    return response.data;
  }

  Future<dynamic> deliverTask(String taskId, String filePath) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath),
    });

    final response = await _dioService.post(
      ApiPaths.deliverTask(taskId),
      data: formData,
    );
    return response.data;
  }

  Future<dynamic> upsertDonor(String taskId, Map<String, dynamic> donorData) async {
    final response = await _dioService.post(
      ApiPaths.upsertDonor(taskId),
      data: donorData,
    );
    return response.data;
  }


  // Legacy/Deprecated methods for backward compatibility
  @Deprecated('Use getReceivedTasks instead')
  Future<dynamic> getAllReceivedTaskRequests() async {
    final response = await _dioService.get(ApiPaths.getAllReceivedTaskRequests);
    return response.data;
  }

  @Deprecated('Use getGivenTasks instead')
  Future<dynamic> getGivenChallenges() async {
    final response = await _dioService.get(ApiPaths.getGivenChallenges);
    return response.data;
  }

  @Deprecated('Use acceptTask instead')
  Future<dynamic> acceptChallenge(String taskId, bool accepted) async {
    if (accepted) {
      final response = await _dioService.post(ApiPaths.acceptChallenge(taskId));
      return response.data;
    } else {
      final response = await _dioService.post(ApiPaths.rejectTask(taskId));
      return response.data;
    }
  }

  @Deprecated('Use deliverTask instead')
  Future<dynamic> deliverTaskRequest(String taskId, String filePath, String postId) async {
    final formData = FormData.fromMap({
      'post_id': postId,
      'file': await MultipartFile.fromFile(filePath),
    });

    final response = await _dioService.post(
      ApiPaths.deliverTaskRequest(taskId),
      data: formData,
    );
    return response.data;
  }
}
