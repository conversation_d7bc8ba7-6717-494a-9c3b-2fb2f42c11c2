import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';

class WalletRepository {
  final HttpService _dioService;

  WalletRepository(this._dioService);

  
  Future<dynamic> getBalance() async {
    final response = await _dioService.get(ApiPaths.getBalance);
    return response.data;
  }

  
  Future<dynamic> getUserTxHistory({int count = 20, int start = 0}) async {
    final response = await _dioService.get(
      ApiPaths.getUserTxHistory,
      queryParameters: {
        'count': count.toString(),
        'start': start.toString(),
      },
    );
    return response.data;
  }

  
  Future<String> createPaymentIntent(String amount) async {
    final response = await _dioService.get(ApiPaths.createPaymentIntent(amount));
    return response.data.toString();
  }


  // Note: Balance SSE endpoint removed as it's not in the API documentation
  // If needed, it should be implemented through WebSocket or polling

  Future<dynamic> withdrawFunds(double amount, {String? method, Map<String, dynamic>? additionalData}) async {
    final data = {
      'amount': amount,
    };

    if (method != null) {
      data['method'] = method;
    }

    if (additionalData != null) {
      data.addAll(additionalData);
    }

    final response = await _dioService.post(
      ApiPaths.withdrawFunds,
      data: data,
    );
    return response.data;
  }
}
