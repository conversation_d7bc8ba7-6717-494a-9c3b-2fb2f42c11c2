import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/utility.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:darve/utils/constants.dart';

class ChatRepository {
  final HttpService _dioService;

  ChatRepository(this._dioService);

  // Discussions API endpoints
  Future<dynamic> getDiscussions() async {
    final response = await _dioService.get(ApiPaths.getDiscussions);
    return response.data;
  }

  Future<dynamic> createDiscussion(Map<String, dynamic> discussionData) async {
    final response = await _dioService.post(
      ApiPaths.createDiscussion,
      data: discussionData,
    );
    return response.data;
  }

  Future<dynamic> updateDiscussion(String discussionId, Map<String, dynamic> discussionData) async {
    final response = await _dioService.patch(
      ApiPaths.updateDiscussion(discussionId),
      data: discussionData,
    );
    return response.data;
  }

  Future<dynamic> deleteDiscussion(String discussionId) async {
    final response = await _dioService.delete(ApiPaths.deleteDiscussion(discussionId));
    return response.data;
  }

  Future<dynamic> postMessageInDiscussion(String discussionId, String content) async {
    final postTitle = generateRandomTitle(32);

    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    final response = await _dioService.post(
      ApiPaths.createPostInDiscussion(discussionId),
      data: formData,
    );
    return response.data;
  }

  Future<dynamic> addChatUsers(String discussionId, List<String> userIds) async {
    final response = await _dioService.post(
      ApiPaths.addChatUsers(discussionId),
      data: {'user_ids': userIds},
    );
    return response.data;
  }

  Future<dynamic> removeChatUsers(String discussionId, List<String> userIds) async {
    final response = await _dioService.delete(
      ApiPaths.removeChatUsers(discussionId),
      data: {'user_ids': userIds},
    );
    return response.data;
  }

  // Discussion SSE endpoints
  Stream<String?> getDiscussionSse(String discussionId) {
    return _dioService.getStream(ApiPaths.getDiscussionSse(discussionId));
  }

  Stream<String?> getDiscussionSseHtmx(String discussionId) {
    return _dioService.getStream(ApiPaths.getDiscussionSseHtmx(discussionId));
  }

  Future<dynamic> getDiscussionById(String discussionId) async {
    final response = await _dioService.get(
      ApiPaths.getDiscussionById(discussionId),
    );
    return response.data;
  }

  // Discussion Tasks
  Future<dynamic> getDiscussionTasks(String discussionId) async {
    final response = await _dioService.get(ApiPaths.getDiscussionTasks(discussionId));
    return response.data;
  }

  Future<dynamic> createDiscussionTask(String discussionId, Map<String, dynamic> taskData) async {
    final response = await _dioService.post(
      ApiPaths.createDiscussionTask(discussionId),
      data: taskData,
    );
    return response.data;
  }

  // Discussion Topics
  Future<dynamic> createOrUpdateTopic(String discussionId, Map<String, dynamic> topicData) async {
    final response = await _dioService.post(
      ApiPaths.createOrUpdateTopic(discussionId),
      data: topicData,
    );
    return response.data;
  }

  Future<dynamic> getTopicForm(String discussionId) async {
    final response = await _dioService.get(ApiPaths.getTopicForm(discussionId));
    return response.data;
  }
}
