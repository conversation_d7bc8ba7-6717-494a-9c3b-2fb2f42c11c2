import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/utility.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:darve/utils/constants.dart';

class ChatRepository {
  final HttpService _dioService;

  ChatRepository(this._dioService);

  // Discussions API endpoints
  Future<dynamic> getDiscussions() async {
    final response = await _dioService.get(ApiPaths.getDiscussions);
    return response.data;
  }

  Future<dynamic> createDiscussion(Map<String, dynamic> discussionData) async {
    final response = await _dioService.post(
      ApiPaths.createDiscussion,
      data: discussionData,
    );
    return response.data;
  }

  Future<dynamic> updateDiscussion(String discussionId, Map<String, dynamic> discussionData) async {
    final response = await _dioService.patch(
      ApiPaths.updateDiscussion(discussionId),
      data: discussionData,
    );
    return response.data;
  }

  Future<dynamic> deleteDiscussion(String discussionId) async {
    final response = await _dioService.delete(ApiPaths.deleteDiscussion(discussionId));
    return response.data;
  }

  Future<dynamic> postMessageInDiscussion(String discussionId, String content) async {
    final postTitle = generateRandomTitle(32);

    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    final response = await _dioService.post(
      ApiPaths.createPostInDiscussion(discussionId),
      data: formData,
    );
    return response.data;
  }

  Future<dynamic> addChatUsers(String discussionId, List<String> userIds) async {
    final response = await _dioService.post(
      ApiPaths.addChatUsers(discussionId),
      data: {'user_ids': userIds},
    );
    return response.data;
  }

  Future<dynamic> removeChatUsers(String discussionId, List<String> userIds) async {
    final response = await _dioService.delete(
      ApiPaths.removeChatUsers(discussionId),
      data: {'user_ids': userIds},
    );
    return response.data;
  }

  // Discussion SSE endpoints
  Stream<String?> getDiscussionSse(String discussionId) {
    return _dioService.getStream(ApiPaths.getDiscussionSse(discussionId));
  }

  Stream<String?> getDiscussionSseHtmx(String discussionId) {
    return _dioService.getStream(ApiPaths.getDiscussionSseHtmx(discussionId));
  }

  Future<dynamic> getDiscussionById(String discussionId) async {
    final response = await _dioService.get(
      ApiPaths.getDiscussionById(discussionId),
    );
    return response.data;
  }

  // Discussion Tasks
  Future<dynamic> getDiscussionTasks(String discussionId) async {
    final response = await _dioService.get(ApiPaths.getDiscussionTasks(discussionId));
    return response.data;
  }

  Future<dynamic> createDiscussionTask(String discussionId, Map<String, dynamic> taskData) async {
    final response = await _dioService.post(
      ApiPaths.createDiscussionTask(discussionId),
      data: taskData,
    );
    return response.data;
  }

  // Discussion Topics
  Future<dynamic> createOrUpdateTopic(String discussionId, Map<String, dynamic> topicData) async {
    final response = await _dioService.post(
      ApiPaths.createOrUpdateTopic(discussionId),
      data: topicData,
    );
    return response.data;
  }

  Future<dynamic> getTopicForm(String discussionId) async {
    final response = await _dioService.get(ApiPaths.getTopicForm(discussionId));
    return response.data;
  }

  // Legacy chat methods that were removed but might be in use
  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getChatsList() async {
    final response = await _dioService.get(ApiPaths.getChatsList);
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> createChatWithUserId(String otherUserId) async {
    final response = await _dioService.get(ApiPaths.createChatWithUserId(otherUserId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> postMessageInChat(String discussionId, String content) async {
    final postTitle = generateRandomTitle(32);

    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    final response = await _dioService.post(
      ApiPaths.postMessageInChat(discussionId),
      data: formData,
    );
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getChatMessages(String chatId, {int limit = 50, int offset = 0}) async {
    final response = await _dioService.get(
      ApiPaths.getChatMessages(chatId),
      queryParameters: {
        'limit': limit,
        'offset': offset,
      },
    );
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> markChatAsRead(String chatId) async {
    final response = await _dioService.post(ApiPaths.markChatAsRead(chatId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> deleteChatMessage(String messageId) async {
    final response = await _dioService.delete(ApiPaths.deleteChatMessage(messageId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> editChatMessage(String messageId, String newContent) async {
    final response = await _dioService.put(
      ApiPaths.editChatMessage(messageId),
      data: {'content': newContent},
    );
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Stream<dynamic> getChatListSse() async* {
    var emittedCount = 0;
    while (true) {
      emittedCount++;
      try {
        yield* _dioService.getStream(ApiPaths.getChatListSse);
      } catch (error) {
        debugPrint(
            'emittedCount: $emittedCount Error during getChatListSse: $error');
        yield {error: error, emittedCount: emittedCount};

        // Wait a bit before retrying to prevent rapid reconnections
        await Future.delayed(const Duration(seconds: 1));
      }
    }
  }
}
