import 'package:darve/services/http/http_service.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:dio/dio.dart';

import '../../utils/utility.dart';
import 'package:darve/utils/constants.dart';

class PostsRepository {
  final HttpService _dioService;

  PostsRepository(this._dioService);

  
  Future<List<PostModel>> getPosts(String username) async {
    final response = await _dioService.get(ApiPaths.getPosts(username));
    
    List<PostModel> allPosts = [];
    for (var element in response.data['posts']) {
      allPosts.add(PostModel.fromJson(element));
    }
    return allPosts;
  }

  
  Future<List<PostModel>> getFollowingPosts() async {
    final response = await _dioService.get(ApiPaths.getFollowingPosts);
    
    List<PostModel> postsList = [];
    for (var post in response.data['post_list']) {
      postsList.add(PostModel.fromJson(post));
    }
    return postsList;
  }

  
  Future<dynamic> createPost(String content, {String filePath = ""}) async {
    final postTitle = generateRandomTitle(32);

    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    if (filePath.isNotEmpty) {
      formData.files.add(MapEntry(
        'file',
        await MultipartFile.fromFile(filePath),
      ));
    }

    final response = await _dioService.post(ApiPaths.createUserPost, data: formData);
    return response.data;
  }


  // Create post in discussion
  Future<dynamic> createPostInDiscussion(String discussionId, String content, {String filePath = ""}) async {
    final postTitle = generateRandomTitle(32);

    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    if (filePath.isNotEmpty) {
      formData.files.add(MapEntry(
        'file_1',
        await MultipartFile.fromFile(filePath),
      ));
    }

    final response = await _dioService.post(ApiPaths.createPostInDiscussion(discussionId), data: formData);
    return response.data;
  }

  Future<bool> createReply(String discussionId, String postUri, String content) async {
    final response = await _dioService.post(
      ApiPaths.createReply(discussionId, postUri),
      data: {
        'title': 'Reply',
        'content': content,
      },
    );

    return response.statusCode == 200;
  }

  
  Future<dynamic> likePost(String postId) async {
    final response = await _dioService.post(ApiPaths.likePost(postId));
    return response.data;
  }

  
  Future<dynamic> unlikePost(String postId) async {
    final response = await _dioService.delete(ApiPaths.unlikePost(postId));
    return response.data;
  }


  // Get all posts
  Future<List<PostModel>> getAllPosts() async {
    final response = await _dioService.get(ApiPaths.getAllPosts);

    List<PostModel> allPosts = [];
    for (var element in response.data['posts']) {
      allPosts.add(PostModel.fromJson(element));
    }
    return allPosts;
  }

  // Get post tasks
  Future<dynamic> getPostTasks(String postId) async {
    final response = await _dioService.get(ApiPaths.getPostTasks(postId));
    return response.data;
  }

  // Create post task
  Future<dynamic> createPostTask(String postId, Map<String, dynamic> taskData) async {
    final response = await _dioService.post(
      ApiPaths.createPostTask(postId),
      data: taskData,
    );
    return response.data;
  }

  // Get post replies
  Future<dynamic> getPostReplies(String discussionId, String postIdent) async {
    final response = await _dioService.get(ApiPaths.getPostReplies(discussionId, postIdent));
    return response.data;
  }

  // Legacy methods that were removed but might be in use
  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getPostDiscussions(String discussionId, String postId) async {
    final response = await _dioService.get(ApiPaths.getPostDiscussions(discussionId, postId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> sharePost(String postId) async {
    final response = await _dioService.post(ApiPaths.sharePost(postId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> deletePost(String postId) async {
    final response = await _dioService.delete(ApiPaths.deletePost(postId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> editPost(String postId, String newContent) async {
    final response = await _dioService.put(
      ApiPaths.editPost(postId),
      data: {'content': newContent},
    );
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getPostComments(String postId) async {
    final response = await _dioService.get(ApiPaths.getPostComments(postId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> addComment(String postId, String comment) async {
    final response = await _dioService.post(
      ApiPaths.addComment(postId),
      data: {'content': comment},
    );
    return response.data;
  }
}
