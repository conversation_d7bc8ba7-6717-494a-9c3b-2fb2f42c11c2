# State Management with ViewModel

## Quick Start Guide

This guide helps newcomers understand our state management system and start using it effectively in 5 minutes.

## What is ViewModel?

**ViewModel** is a simple wrapper that holds your data and its current state (loading, loaded, empty, or error). Think of it as a smart container that tells your UI exactly what to display.

### **The 4 States Every Screen Has**

1. **Loading** - "Please wait, fetching data..."
2. **Content** - "Here's your data!"
3. **Empty** - "No data found"
4. **Error** - "Something went wrong"

### **How to Use ViewModel (3 Steps)**

**Step 1: Declare your state variable**
```dart
final Rx<ViewModel<List<Product>>> productsState = ViewModel<List<Product>>.loading().obs;
```

**Step 2: Update state when loading data**
```dart
// Set loading
productsState.value = ViewModel<List<Product>>.loading();

// Set content when data arrives
productsState.value = ViewModel<List<Product>>.content(productList);

// Set error if something fails
productsState.value = ViewModel<List<Product>>.error("Failed to load products");
```

**Step 3: Display in UI**
```dart
Obx(() => EntityStateWidget<List<Product>>(
  model: controller.productsState,
  onRetry: () => controller.loadProducts(),
  itemBuilder: (products) => ProductList(products: products),
))
```

## Essential Functions You'll Use Daily

### **For Controllers**

| Function | When to Use | Example |
|----------|-------------|---------|
| `ViewModel.loading()` | Starting any data fetch | `state.value = ViewModel.loading()` |
| `ViewModel.content(data)` | When data loads successfully | `state.value = ViewModel.content(users)` |
| `ViewModel.empty()` | When no data is found | `state.value = ViewModel.empty()` |
| `ViewModel.error(error)` | When something goes wrong | `state.value = ViewModel.error("Network error")` |
| `result.toListViewModel()` | Convert API result to ViewModel | `state.value = result.toListViewModel()` |

### **For UI Widgets**

| Widget | Purpose | When to Use |
|--------|---------|-------------|
| `EntityStateWidget` | Automatically handles all 4 states | Every screen that loads data |
| `Obx(() => ...)` | Makes widget reactive to state changes | Wrap around EntityStateWidget |
| `onRetry: () => controller.reload()` | Retry button for errors | Always provide retry functionality |

## Learn from Working Examples

### **Perfect Implementation Examples**

| Example Class | What It Shows | Key Learning |
|---------------|---------------|--------------|
| `ProfileInsightsController` | Complete Result-based pattern | Copy the `loadFollowers()` and `loadFollowing()` methods |
| `ProfileInsightsPage` | EntityStateWidget usage | See how `Obx()` wraps EntityStateWidget |
| `DefaultErrorWidget` | Smart error display | Handles both string and AppError types |
| `ProfileRepository` | Modern Result-based methods | All methods end with "Result" suffix |
| `LegacyProfileRepository` | Old exception-based methods | What NOT to copy for new code |

### **Quick Copy-Paste Patterns**

**For Controllers (copy from ProfileInsightsController):**
- `loadFollowers()` method → Pattern for loading lists
- `loadFollowing()` method → Same pattern, different data
- `unfollowUser()` method → Pattern for actions with side effects

**For UI (copy from ProfileInsightsPage):**
- `EntityStateWidget` usage → How to display any data type
- `Obx()` wrapping → Making widgets reactive
- `onRetry` callbacks → Providing retry functionality

## Quick Reference: What Each File Does

| File Location | What It Contains | When You Need It |
|---------------|------------------|------------------|
| `lib/ui/core/entity_state_widget.dart` | The magic widget that handles all states | Every screen that loads data |
| `lib/utils/result.dart` | Helper functions like `toListViewModel()` | When converting API results |
| `lib/services/error/error_models.dart` | Error types and severity levels | When handling specific errors |
| `lib/ui/profile/insights/profile_insights_controller.dart` | Example of perfect implementation | Copy this pattern for new controllers |

## Troubleshooting Guide

### **Common Issues & Solutions**

| Problem | Solution | Where to Look |
|---------|----------|---------------|
| "UI not updating" | Wrap with `Obx()` | See `ProfileInsightsPage` for examples |
| "Always shows loading" | Check if you're setting content state | Copy pattern from `ProfileInsightsController.loadFollowers()` |
| "Error not displaying" | Ensure onRetry is provided | Check `EntityStateWidget` usage in examples |
| "Empty state not showing" | Use `toListViewModel()` for lists | See `result.dart` extension methods |

### **Quick Debugging**

Add these debug prints to your controller methods:
- `print('Current state: ${controller.itemsState.value.state}');`
- `print('Has data: ${controller.itemsState.value.data != null}');`
- `print('Error: ${controller.itemsState.value.error}');`

### **Force States for Testing**

Test your UI by forcing different states in your controller:
- Loading: `state.value = ViewModel<List<Item>>.loading();`
- Error: `state.value = ViewModel<List<Item>>.error("Test error");`
- Empty: `state.value = ViewModel<List<Item>>.empty();`

## Migration from Old Code

### **Modernizing Controllers**

| Old Pattern | New Pattern | Example File |
|-------------|-------------|--------------|
| try-catch blocks | Result.fold() or extension methods | `ProfileInsightsController` |
| Manual state assignments | Direct assignment from fold() | `loadFollowers()` method |
| Exception-based repos | Result-based repos | `ProfileRepository` vs `LegacyProfileRepository` |

### **Step-by-Step Migration**

1. **Replace Repository Calls**: Change from `repository.getData()` to `repository.getDataResult()`
2. **Remove try-catch**: Replace with `result.toListViewModel()` or `result.fold()`
3. **Update State Assignment**: Use direct assignment pattern from examples
4. **Add Error Handling**: Use `onError` callbacks for side effects

## Performance Tips

### **Efficient Reactive Updates**
- Use `Obx()` only around widgets that need to react to state changes
- Don't wrap entire pages in `Obx()` - be granular like in `ProfileInsightsPage`
- Use `const` constructors for ViewModel states when possible

### **Memory Management**
- Controllers are automatically disposed by GetX bindings
- Large data sets are cleared when ViewModel state changes
- Use `Get.find()` instead of `Get.put()` for shared controllers

## Real Examples to Study

### **Perfect Implementation Examples**

| File | What It Demonstrates | Key Learning Points |
|------|---------------------|-------------------|
| `ProfileInsightsController` | Complete modern controller | State management, Result handling, error management |
| `ProfileInsightsPage` | EntityStateWidget usage | UI integration, reactive updates, retry functionality |
| `DefaultErrorWidget` | Smart error display | Dynamic error handling, debug info, user experience |
| `ProfileRepository` | Modern API layer | Result-based methods, error context, clean architecture |

### **UI Integration Patterns**

**Study `ProfileInsightsPage` for:**
- How to use `Obx()` effectively
- EntityStateWidget integration
- Retry functionality implementation
- Multiple state management on one page

**Study `DefaultErrorWidget` for:**
- Dynamic error type handling (String vs AppError)
- Debug information display
- Error severity-based UI
- User-friendly error messages

### **Controller Patterns**

**Study `ProfileInsightsController` for:**
- Modern state variable declaration
- Result-based data loading
- Error handling without try-catch
- Functional programming patterns
- Dependency injection

### **Repository Patterns**

**Compare these files:**
- `ProfileRepository` (modern Result-based) ✅
- `LegacyProfileRepository` (old exception-based) ❌

**Key differences:**
- Return types: `Result<T>` vs direct types
- Error handling: Wrapped vs thrown exceptions
- Method naming: `getDataResult()` vs `getData()`

## Advanced Patterns

### **Conditional State Updates**

```dart
Future<void> loadItemsIfNeeded() async {
  // Only load if not already loaded or in error state
  if (itemsState.value.state == ViewState.content) {
    return; // Already loaded
  }
  
  await loadItems();
}
```

### **Optimistic Updates**

```dart
Future<void> toggleFavorite(String itemId) async {
  // Optimistically update UI
  final currentItems = itemsState.value.data ?? [];
  final updatedItems = currentItems.map((item) {
    if (item.id == itemId) {
      return item.copyWith(isFavorite: !item.isFavorite);
    }
    return item;
  }).toList();
  
  itemsState.value = ViewModel<List<MyModel>>.content(updatedItems);
  
  // Perform actual update
  final result = await repository.toggleFavoriteResult(itemId);
  
  result.fold(
    (error) {
      // Revert on error
      itemsState.value = ViewModel<List<MyModel>>.content(currentItems);
      _errorHandler.displayErrorToast(error, 'toggleFavorite');
    },
    (updatedItem) {
      // Update with server response
      final finalItems = currentItems.map((item) {
        return item.id == itemId ? updatedItem : item;
      }).toList();
      itemsState.value = ViewModel<List<MyModel>>.content(finalItems);
    },
  );
}
```

### **Pagination Support**

```dart
class PaginatedController extends GetxController {
  final Rx<ViewModel<List<MyModel>>> itemsState = 
      const ViewModel<List<MyModel>>.loading().obs;
  
  final RxBool isLoadingMore = false.obs;
  int _currentPage = 1;
  bool _hasMoreData = true;

  Future<void> loadItems({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
      _hasMoreData = true;
      itemsState.value = const ViewModel<List<MyModel>>.loading();
    }
    
    final result = await repository.getItemsResult(page: _currentPage);
    
    result.fold(
      (error) {
        if (isRefresh) {
          itemsState.value = ViewModel<List<MyModel>>.error(error);
        }
        _errorHandler.displayErrorToast(error, 'loadItems');
      },
      (newItems) {
        final currentItems = isRefresh ? <MyModel>[] : (itemsState.value.data ?? <MyModel>[]);
        final allItems = [...currentItems, ...newItems];
        
        _hasMoreData = newItems.isNotEmpty;
        _currentPage++;
        
        itemsState.value = allItems.isEmpty 
            ? const ViewModel<List<MyModel>>.empty()
            : ViewModel<List<MyModel>>.content(allItems);
      },
    );
  }

  Future<void> loadMore() async {
    if (!_hasMoreData || isLoadingMore.value) return;
    
    isLoadingMore.value = true;
    await loadItems();
    isLoadingMore.value = false;
  }
}
```

## Testing Patterns

### **State Testing**

```dart
group('ViewModel State Management', () {
  test('should start with loading state', () {
    expect(controller.itemsState.value.state, ViewState.loading);
  });

  test('should set content state when data loads successfully', () async {
    // Arrange
    when(mockRepository.getItemsResult())
        .thenAnswer((_) async => Right([mockItem1, mockItem2]));

    // Act
    await controller.loadItems();

    // Assert
    expect(controller.itemsState.value.state, ViewState.content);
    expect(controller.itemsState.value.data, [mockItem1, mockItem2]);
  });

  test('should set error state when loading fails', () async {
    // Arrange
    final error = NetworkError(message: 'Network error', code: 'NETWORK_ERROR');
    when(mockRepository.getItemsResult())
        .thenAnswer((_) async => Left(error));

    // Act
    await controller.loadItems();

    // Assert
    expect(controller.itemsState.value.state, ViewState.error);
    expect(controller.itemsState.value.error, error);
  });
});
```

### **Widget Testing**

```dart
testWidgets('should display loading widget initially', (tester) async {
  await tester.pumpWidget(MyApp());
  
  expect(find.byType(CircularProgressIndicator), findsOneWidget);
});

testWidgets('should display items when loaded', (tester) async {
  controller.itemsState.value = ViewModel.content([mockItem1, mockItem2]);
  
  await tester.pumpWidget(MyApp());
  await tester.pump();
  
  expect(find.byType(MyItemWidget), findsNWidgets(2));
});
```

## Performance Considerations

### **Efficient State Updates**
- Use `Obx()` only around widgets that need to react to state changes
- Avoid wrapping entire pages in `Obx()` - be granular
- Use `const` constructors for ViewModel states when possible

### **Memory Management**
- Dispose controllers properly in bindings
- Use `Get.find()` instead of `Get.put()` for shared controllers
- Clear large data sets when navigating away from pages

## Next Steps

### **Learn More**
1. **[Error Handling Architecture](./error_handling_architecture.md)** - Understand the complete error system
2. **[EntityStateWidget Guide](./entitystatewidget_implementation_guide.md)** - Master the UI component
3. **[Result Patterns](./result_to_viewmodel_standard_pattern.md)** - Advanced functional patterns

### **Start Implementing**
1. Study `ProfileInsightsController` - copy its patterns
2. Use `EntityStateWidget` in your UI - follow `ProfileInsightsPage`
3. Replace old try-catch code with Result-based patterns
4. Add proper error handling with `onError` callbacks

### **Get Help**
- Check the troubleshooting section above
- Study the example files mentioned throughout this guide
- Look at existing working implementations before writing new code

This architecture provides a solid foundation for building maintainable, user-friendly Flutter applications with consistent error handling and state management.
