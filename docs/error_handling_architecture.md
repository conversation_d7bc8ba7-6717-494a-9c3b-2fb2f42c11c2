# Error Handling Architecture

## Quick Start for Newcomers

This guide explains our error handling system and how to use it effectively.

## What You Need to Know

### **The Big Picture**
1. **No more try-catch blocks** in controllers
2. **Result types** wrap success/error states
3. **AppError hierarchy** provides rich error information
4. **Automatic error handling** in UI components

### **Key Files to Study**

| File | Purpose | What to Learn |
|------|---------|---------------|
| `lib/services/error/error_models.dart` | Error types and severity | How errors are structured |
| `lib/utils/result.dart` | Result type and extensions | How to convert Results to ViewModels |
| `lib/ui/core/entity_state_widget.dart` | Error display logic | How errors are shown to users |
| `ProfileInsightsController` | Perfect error handling example | How to handle errors in controllers |

## Error Types You'll Encounter

### **Error Severity Levels**

| Severity | When to Use | UI Behavior | Example |
|----------|-------------|-------------|---------|
| `expected` | Normal business errors | Gray icon, calm message | "User not found" |
| `warning` | Recoverable issues | Orange icon, retry suggested | "Network timeout" |
| `unexpected` | System errors | Red icon, technical details | "JSON parsing failed" |
| `fatal` | Critical failures | Red icon, restart suggested | "Out of memory" |

### **Common Error Types**

| Error Class | Purpose | Example Usage |
|-------------|---------|---------------|
| `UserError` | User-facing messages | "Please check your internet connection" |
| `ValidationError` | Input validation | "Email format is invalid" |
| `NetworkError` | Network issues | "Unable to connect to server" |
| `ServerError` | API errors | "Server returned error 500" |

## How to Use Result Types

### **In Repository Methods**
- Return `Result<T>` instead of throwing exceptions
- Use `ResultHelper.tryCallAsync()` to wrap operations
- Study `ProfileRepository` for perfect examples

### **In Controllers**
- Use `result.toListViewModel()` for lists
- Use `result.toViewModel()` for single items
- Study `ProfileInsightsController.loadFollowers()` method

### **In UI Components**
- Use `EntityStateWidget` to display states
- Provide `onRetry` callbacks for error recovery
- Study `ProfileInsightsPage` for implementation

## The 3-Layer Error Handling System

### **Layer 1: Repository (API Calls)**
**What it does:** Catches all errors and wraps them in Result types
**Your job:** Use `ResultHelper.tryCallAsync()` for all async operations
**Example:** Study `ProfileRepository.getProfileDataResult()` method

**Key points:**
- Never throw exceptions - always return `Result<T>`
- Add helpful error messages and context
- Use appropriate error codes for tracking

### **Layer 2: Controller (Business Logic)**
**What it does:** Converts Results to ViewModel states
**Your job:** Use extension methods like `toListViewModel()`
**Example:** Study `ProfileInsightsController.loadFollowers()` method

**Key points:**
- No try-catch blocks needed
- Use `result.toListViewModel()` for lists
- Use `result.toViewModel()` for single items
- Handle side effects in `onError` callbacks

### **Layer 3: UI (Display)**
**What it does:** Shows appropriate UI for each state
**Your job:** Use `EntityStateWidget` with proper retry callbacks
**Example:** Study `ProfileInsightsPage` implementation

**Key points:**
- Wrap with `Obx()` for reactivity
- Always provide `onRetry` functionality
- Let EntityStateWidget handle state display logic

## Error Propagation Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Repository    │    │   Controller     │    │       UI        │
│                 │    │                  │    │                 │
│ HTTP Error      │───▶│ Result<T>        │───▶│ EntityStateWidget│
│ Parse Error     │    │ .fold()          │    │ Error Display   │
│ Network Error   │    │ .toViewModel()   │    │ Retry Button    │
│                 │    │                  │    │                 │
│ ↓ Wrap in       │    │ ↓ Convert to     │    │ ↓ Render        │
│ AppError        │    │ ViewModel        │    │ Error State     │
│ ↓ Add Context   │    │ ↓ Update State   │    │ ↓ Show Debug    │
│ ↓ Return Result │    │ ↓ Handle Effects │    │ Info (dev mode) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Error Context Enhancement

### **Automatic Context Addition**

```dart
class ResultHelper {
  static Future<Result<T>> tryCallAsync<T>(
    Future<T> Function() operation, {
    required String errorMessage,
    required String errorCode,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final result = await operation();
      return Right(result);
    } catch (e) {
      // Enhanced error context
      final enhancedMetadata = {
        ...?metadata,
        'timestamp': DateTime.now().toIso8601String(),
        'operation': errorCode,
        'stackTrace': StackTrace.current.toString(),
      };
      
      if (e is AppError) {
        return Left(e.copyWith(metadata: enhancedMetadata));
      }
      
      return Left(UserError(
        message: errorMessage,
        code: errorCode,
        originalError: e,
        metadata: enhancedMetadata,
      ));
    }
  }
}
```

## Global Error Reporting

### **Error Reporting Service Integration**

```dart
class ErrorHandler {
  void displayErrorToast(dynamic error, String operation) {
    if (error is AppError) {
      // Report to analytics/crash reporting
      ErrorReportingService.reportError(
        error: error,
        operation: operation,
        severity: error.severity,
      );
      
      // Show user-friendly message
      _showToast(error.message);
      
      // Log detailed info in debug mode
      if (kDebugMode) {
        debugPrint('Error in $operation: ${error.toDebugString()}');
      }
    } else {
      // Handle legacy string errors
      _showToast(error.toString());
    }
  }
}
```

## Migration Strategy

### **Phase 1: Repository Layer** ✅
- [x] Create Result-based repository methods
- [x] Implement LegacyRepository for backward compatibility
- [x] Add ResultHelper utilities

### **Phase 2: Controller Layer** 🔄
- [x] ProfileInsightsController (Complete)
- [ ] ProfileController (In Progress)
- [ ] Other controllers (Planned)

### **Phase 3: UI Layer** ✅
- [x] EntityStateWidget implementation
- [x] Dynamic error handling
- [x] Debug mode enhancements

### **Phase 4: Cleanup** 🎯
- [ ] Remove legacy repositories
- [ ] Update all controllers
- [ ] Comprehensive testing

## Best Practices

### ✅ **Do**
- Use Result types for all async operations
- Implement proper error context and metadata
- Use functional patterns (fold, extension methods)
- Provide user-friendly error messages
- Include debug information in development

### ❌ **Don't**
- Throw exceptions in repository layer
- Use try-catch in controllers (use Result.fold instead)
- Assign state inside fold callbacks
- Show technical error messages to users
- Ignore error reporting and analytics

## Testing Your Error Handling

### **Controller Tests**
Test that your controllers handle errors correctly:
```dart
test('should set error state when API fails', () async {
  // Arrange: Mock repository to return error
  when(mockRepository.getDataResult())
      .thenAnswer((_) async => Left(NetworkError(message: 'Network failed', code: 'NETWORK_ERROR')));

  // Act: Call controller method
  await controller.loadData();

  // Assert: Check error state is set
  expect(controller.dataState.value.state, ViewState.error);
  expect(controller.dataState.value.error, isA<NetworkError>());
});
```

### **Widget Tests**
Test that your UI displays errors correctly:
```dart
testWidgets('should show error widget when state is error', (tester) async {
  // Arrange: Set error state
  controller.dataState.value = ViewModel.error('Test error');

  // Act: Build widget
  await tester.pumpWidget(MyApp());

  // Assert: Check error UI is displayed
  expect(find.text('Test error'), findsOneWidget);
  expect(find.text('Retry'), findsOneWidget);
});
```

## Next Steps

### **Learn More**
1. **[State Management with ViewModel](./state_management_viewmodel.md)** - Complete state management guide
2. **[EntityStateWidget Guide](./entitystatewidget_implementation_guide.md)** - UI component mastery
3. **[Result Patterns](./result_to_viewmodel_standard_pattern.md)** - Advanced functional patterns

### **Start Implementing**
1. Replace try-catch blocks with Result-based patterns
2. Use `ResultHelper.tryCallAsync()` in repository methods
3. Convert controllers to use `result.toListViewModel()`
4. Add proper error context and metadata

This error handling architecture provides a robust foundation that improves user experience, developer productivity, and application reliability.
