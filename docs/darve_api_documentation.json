{"info": {"name": "Darve API Collection", "description": "Complete API documentation for the Rust web application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\", \"password\": \"password123\"}"}, "url": {"raw": "{{baseUrl}}/api/login", "host": ["{{baseUrl}}"], "path": ["api", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    ", "    // Extract user ID in format: tb:id.String", "    const userId = response.user.id.tb + ':' + response.user.id.id.String;", "    const username = response.user.username;", "    const token = response.token;", "    ", "    // Save to collection variables", "    pm.collectionVariables.set('userId', userId);", "    pm.collectionVariables.set('username', username);", "    pm.collectionVariables.set('authToken', token);", "    ", "    console.log('Saved userId:', userId);", "    console.log('Saved username:', username);", "    console.log('Saved token:', token);", "}"], "type": "text/javascript"}}]}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\", \"password\": \"password123\", \"username\": \"newuser\"}"}, "url": {"raw": "{{baseUrl}}/api/register", "host": ["{{baseUrl}}"], "path": ["api", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    ", "    // Extract user ID in format: tb:id.String", "    const userId = response.user.id.tb + ':' + response.user.id.id.String;", "    const username = response.user.username;", "    const token = response.token;", "    ", "    // Save to collection variables", "    pm.collectionVariables.set('userId', userId);", "    pm.collectionVariables.set('username', username);", "    pm.collectionVariables.set('authToken', token);", "    ", "    console.log('Saved userId:', userId);", "    console.log('Saved username:', username);", "    console.log('Saved token:', token);", "}"], "type": "text/javascript"}}]}, {"name": "Sign with Facebook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"token\": \"facebook_token_here\"}"}, "url": {"raw": "{{baseUrl}}/api/auth/sign_with_facebook", "host": ["{{baseUrl}}"], "path": ["api", "auth", "sign_with_facebook"]}}}, {"name": "Sign with Google", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/auth/sign_with_google"}}, {"name": "Sign with Apple", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/auth/sign_with_apple"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/forgot_password"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/reset_password"}}]}, {"name": "Posts", "item": [{"name": "Get Posts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/posts"}}, {"name": "Create Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Post Title", "type": "text"}, {"key": "content", "value": "Post content here", "type": "text"}, {"key": "topic_id", "value": "topic_id_here", "type": "text"}, {"key": "tags", "value": "tag1,tag2", "type": "text"}, {"key": "file_1", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/api/discussion/:discussion_id/post", "host": ["{{baseUrl}}"], "path": ["api", "discussion", ":discussion_id", "post"]}}}, {"name": "Like Post", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/posts/:post_id/like"}}, {"name": "Unlike Post", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/posts/:post_id/unlike"}}, {"name": "Create Task for Post", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/posts/:post_id/tasks"}}, {"name": "Get Post Tasks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/posts/:post_id/tasks"}}]}, {"name": "Discussions", "item": [{"name": "Get Discussions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions"}}, {"name": "Create Discussion", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions"}}, {"name": "Update Discussion", "request": {"method": "PATCH", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions/:discussion_id"}}, {"name": "Delete Discussion", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions/:discussion_id"}}, {"name": "Create Discussion Task", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions/:discussion_id/tasks"}}, {"name": "Get Discussion Tasks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions/:discussion_id/tasks"}}, {"name": "Add Chat Users", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions/:discussion_id/chat_users"}}, {"name": "Remove Chat Users", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussions/:discussion_id/chat_users"}}, {"name": "Discussion SSE", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/event-stream"}], "url": "{{baseUrl}}/api/discussion/:discussion_id/sse"}}, {"name": "Discussion SSE HTMX", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/event-stream"}], "url": "{{baseUrl}}/api/discussion/:discussion_id/sse/htmx"}}]}, {"name": "Tasks", "item": [{"name": "Get Received Tasks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/tasks/received"}}, {"name": "Get Given Tasks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/tasks/given"}}, {"name": "Accept Task", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/tasks/:task_id/accept"}}, {"name": "Reject Task", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/tasks/:task_id/reject"}}, {"name": "Deliver Task", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/tasks/:task_id/deliver"}}, {"name": "Upsert Donor", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/tasks/:task_id/donor"}}]}, {"name": "Notifications", "item": [{"name": "Get Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/notifications"}}, {"name": "Read All Notifications", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/notifications/read"}}, {"name": "Get Notification Count", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/notifications/count"}}, {"name": "Read Notification", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/notifications/:notification_id/read"}}, {"name": "Notifications SSE", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/event-stream"}], "url": "{{baseUrl}}/api/notifications/sse"}}]}, {"name": "Community", "item": [{"name": "Create/Update Community", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/community"}}]}, {"name": "Discussion Topics", "item": [{"name": "Create/Update Topic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"id\": \"topic_id\", \"title\": \"Topic Title\", \"hidden\": \"false\", \"access_rule_id\": \"rule_id\"}"}, "url": "{{baseUrl}}/api/discussion/:discussion_id/topic"}}, {"name": "Get Topic Form", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussion/:discussion_id/topic"}}]}, {"name": "Replies", "item": [{"name": "Create Reply", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"title\": \"Reply Title\", \"content\": \"Reply content here\"}"}, "url": "{{baseUrl}}/api/discussion/:discussion_id/post/:post_uri/reply"}}, {"name": "Get Post Replies", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/discussion/:discussion_id/post/:post_ident/replies"}}]}, {"name": "User Management", "item": [{"name": "Get User Posts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/user/{{username}}/posts", "host": ["{{baseUrl}}"], "path": ["api", "user", "{{username}}", "posts"]}}}, {"name": "Create User Post", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/user/post"}}, {"name": "Search Users", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/user/search"}}, {"name": "Update Profile", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/accounts/edit"}}, {"name": "Reset Password", "request": {"method": "PATCH", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/users/current/password"}}, {"name": "Set Password", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/users/current/password"}}, {"name": "Start Email Verification", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/users/current/email/verification/start"}}, {"name": "Confirm Email Verification", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/users/current/email/verification/confirm"}}]}, {"name": "Follow System", "item": [{"name": "Get Followers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/user/{{userId}}/followers", "host": ["{{baseUrl}}"], "path": ["api", "user", "{{userId}}", "followers"]}}}, {"name": "Get Following", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/user/{{userId}}/following", "host": ["{{baseUrl}}"], "path": ["api", "user", "{{userId}}", "following"]}}}, {"name": "Follow User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/follow/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "follow", "{{userId}}"]}}}, {"name": "Unfollow User", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/follow/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "follow", "{{userId}}"]}}}, {"name": "Check if Following", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/user/follows/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "user", "follows", "{{userId}}"]}}}, {"name": "Get Following Posts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/u/following/posts"}}]}, {"name": "Access Rules", "item": [{"name": "Get Access Rule Form", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/community/:target_record_id/access-rule"}}, {"name": "Create/Update Access Rule", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/access-rule"}}, {"name": "Join Access Rule", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/access-rule/:access_rule_id/join"}}, {"name": "Save Access Gain Action", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\", \"access_rule_id\": \"rule_id\", \"next\": \"/redirect_url\"}"}, "url": "{{baseUrl}}/api/join/access-rule"}}]}, {"name": "Wallet", "item": [{"name": "Get Wallet History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/user/wallet/history"}}, {"name": "Get User Balance", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/user/wallet/balance"}}, {"name": "Withdraw", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/user/wallet/withdraw"}}, {"name": "Request Endowment Intent", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/user/wallet/endowment/:amount"}}]}, {"name": "Stripe Integration", "item": [{"name": "Access Rule Payment", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/stripe/access-rule/:ar_id"}}, {"name": "Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/stripe/webhook"}}, {"name": "Stripe Endowment Webhook", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/stripe/endowment/webhook"}}]}, {"name": "WebAuthn/Passkey", "item": [{"name": "Start Passkey Registration", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/passkey/register_start/{{username}}", "host": ["{{baseUrl}}"], "path": ["api", "passkey", "register_start", "{{username}}"]}}}, {"name": "Finish Passkey Registration", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/passkey/register_finish"}}, {"name": "Start <PERSON><PERSON> Login", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/passkey/login_start/{{username}}", "host": ["{{baseUrl}}"], "path": ["api", "passkey", "login_start", "{{username}}"]}}}, {"name": "Finish <PERSON>key Login", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/api/passkey/login_finish"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "username", "value": "", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}]}